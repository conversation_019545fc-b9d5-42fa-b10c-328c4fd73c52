# 🌐 域名迁移故障排除指南

## 📊 当前状态
- **域名**: cloudgu.xyz
- **服务器IP**: *************
- **问题**: 从IP地址切换到域名后WebSocket连接失败

## 🔍 诊断步骤

### 1. 验证DNS解析
```bash
# 检查DNS解析是否正确
nslookup cloudgu.xyz
dig cloudgu.xyz

# 预期结果: 应该返回 *************
```

### 2. 检查SSL证书
```bash
# 测试SSL连接
openssl s_client -connect cloudgu.xyz:443 -servername cloudgu.xyz

# 检查证书主题名称
openssl x509 -in /etc/ssl/certs/nginx-selfsigned.crt -text -noout | grep "Subject:"
```

### 3. 验证Nginx配置
```bash
# 检查Nginx配置语法
sudo nginx -t

# 查看当前配置
sudo nginx -T | grep -A 10 -B 5 "server_name cloudgu.xyz"

# 检查Nginx错误日志
sudo tail -20 /var/log/nginx/error.log
```

### 4. 测试端口连通性
```bash
# 测试HTTP连接
curl -I http://cloudgu.xyz:8080

# 测试HTTPS连接
curl -k -I https://cloudgu.xyz

# 测试WebSocket连接 (需要安装wscat)
npm install -g wscat
wscat -c ws://cloudgu.xyz:8080
```

## 🛠️ 修复步骤

### 步骤1: 重新生成SSL证书
```bash
# 为域名重新生成自签名证书
sudo openssl genrsa -out /etc/ssl/private/nginx-selfsigned.key 2048
sudo openssl req -new -x509 -key /etc/ssl/private/nginx-selfsigned.key -out /etc/ssl/certs/nginx-selfsigned.crt -days 365 -subj "/C=CN/ST=State/L=City/O=Organization/CN=cloudgu.xyz"

# 设置权限
sudo chmod 600 /etc/ssl/private/nginx-selfsigned.key
sudo chmod 644 /etc/ssl/certs/nginx-selfsigned.crt
```

### 步骤2: 应用Nginx配置
```bash
# 应用更新后的配置
sudo cp ~/DataMonitor/websocket-server/nginx-selfsigned.conf /etc/nginx/sites-available/datamonitor
sudo ln -sf /etc/nginx/sites-available/datamonitor /etc/nginx/sites-enabled/

# 测试并重新加载
sudo nginx -t && sudo systemctl reload nginx
```

### 步骤3: 验证服务运行
```bash
# 确保WebSocket服务运行
netstat -tlnp | grep 8080

# 确保Nginx监听443端口
netstat -tlnp | grep 443

# 检查防火墙
sudo ufw status
```

## 🎯 测试验证

### 浏览器测试
1. 访问: `https://cloudgu.xyz`
2. 手动信任自签名证书
3. 访问: `https://data-monitor-t4je.vercel.app/`
4. 检查浏览器控制台WebSocket连接状态

### 预期日志输出
```
[Realtime Store] WebSocket URL: wss://cloudgu.xyz
[Realtime Store] 当前页面协议: https:
[WebSocket] 尝试连接到: wss://cloudgu.xyz
[WebSocket] 连接成功
```

## 🚨 常见问题解决

### 问题1: DNS解析失败
```bash
# 检查域名是否正确配置A记录指向 *************
# 可能需要等待DNS传播 (最多24小时)
```

### 问题2: SSL证书不匹配
```bash
# 确保证书CN字段是域名而不是IP
openssl x509 -in /etc/ssl/certs/nginx-selfsigned.crt -text -noout | grep "CN="
```

### 问题3: 浏览器缓存
- 清除浏览器缓存
- 使用无痕模式测试
- 重新信任SSL证书

## 📞 下一步建议

### 临时解决方案
如果域名解析有问题，可以临时修改本地hosts文件:
```
# Windows: C:\Windows\System32\drivers\etc\hosts
# Linux/Mac: /etc/hosts
************* cloudgu.xyz
```

### 长期解决方案
建议使用Let's Encrypt获取真正的SSL证书:
```bash
sudo certbot --nginx -d cloudgu.xyz --email <EMAIL>
```
