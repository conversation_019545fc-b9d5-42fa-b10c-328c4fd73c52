<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>工厂车间地图组件演示</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .demo-header {
            text-align: center;
            margin-bottom: 30px;
        }
        .demo-header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        .demo-header p {
            color: #666;
            font-size: 1.1em;
        }
        .demo-section {
            background: white;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        .demo-section h2 {
            color: #333;
            margin-top: 0;
            border-bottom: 2px solid #4A90E2;
            padding-bottom: 10px;
        }
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .feature-item {
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            background: #fafafa;
        }
        .feature-item h3 {
            margin-top: 0;
            color: #4A90E2;
        }
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 14px;
            overflow-x: auto;
            margin: 10px 0;
        }
        .status-demo {
            display: flex;
            gap: 15px;
            align-items: center;
            margin: 10px 0;
        }
        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
        }
        .status-online { background-color: #4caf50; }
        .status-warning { background-color: #ff9800; }
        .status-offline { background-color: #f44336; }
        .status-error { background-color: #9e9e9e; }
        .access-info {
            background: #e3f2fd;
            border: 1px solid #2196f3;
            border-radius: 6px;
            padding: 15px;
            margin: 20px 0;
        }
        .access-info h3 {
            margin-top: 0;
            color: #1976d2;
        }
        .access-link {
            display: inline-block;
            background: #4A90E2;
            color: white;
            padding: 10px 20px;
            text-decoration: none;
            border-radius: 4px;
            margin: 10px 10px 10px 0;
        }
        .access-link:hover {
            background: #357ABD;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="demo-header">
            <h1>🏭 工厂车间地图组件</h1>
            <p>基于Vue 3 + TypeScript + Element Plus的交互式工厂地图组件</p>
        </div>

        <div class="access-info">
            <h3>🚀 快速访问</h3>
            <p>组件已集成到项目中，您可以通过以下方式访问：</p>
            <a href="http://localhost:5173/" class="access-link">监控中心首页</a>
            <a href="http://localhost:5173/factory-map" class="access-link">工厂地图页面</a>
        </div>

        <div class="demo-section">
            <h2>✨ 主要功能</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>🗺️ 区域划分</h3>
                    <p>支持5个功能区域：生产区、仓储区、办公区、检测区、维护区</p>
                    <p>每个区域使用不同颜色标识，便于区分</p>
                </div>
                <div class="feature-item">
                    <h3>🔧 设备管理</h3>
                    <p>支持21个设备点位的实时监控</p>
                    <p>每个设备具有唯一的位置编码（如"1区3排"）</p>
                </div>
                <div class="feature-item">
                    <h3>📊 状态监控</h3>
                    <p>实时显示设备状态：</p>
                    <div class="status-demo">
                        <span class="status-indicator status-online"></span>在线
                        <span class="status-indicator status-warning"></span>警告
                        <span class="status-indicator status-offline"></span>离线
                        <span class="status-indicator status-error"></span>错误
                    </div>
                </div>
                <div class="feature-item">
                    <h3>🖱️ 交互功能</h3>
                    <p>• 点击设备查看详细信息</p>
                    <p>• 鼠标滚轮缩放地图</p>
                    <p>• 拖拽平移浏览</p>
                    <p>• 按钮控制视图</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>🛠️ 技术实现</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>前端技术栈</h3>
                    <p>• Vue 3 Composition API</p>
                    <p>• TypeScript 类型安全</p>
                    <p>• Element Plus UI组件</p>
                    <p>• SVG 2D渲染</p>
                </div>
                <div class="feature-item">
                    <h3>响应式设计</h3>
                    <p>• 自适应不同屏幕尺寸</p>
                    <p>• 移动端友好</p>
                    <p>• 流畅的动画效果</p>
                    <p>• 现代浏览器兼容</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📝 使用示例</h2>
            <h3>基本使用</h3>
            <div class="code-block">
&lt;template&gt;
  &lt;div class="map-container"&gt;
    &lt;FactoryMap /&gt;
  &lt;/div&gt;
&lt;/template&gt;

&lt;script setup lang="ts"&gt;
import FactoryMap from '@/components/FactoryMap.vue'
&lt;/script&gt;
            </div>

            <h3>设备数据结构</h3>
            <div class="code-block">
interface FactoryDevice {
  id: string                    // 设备唯一ID
  name: string                  // 设备名称
  type: string                  // 设备类型
  x: number                     // SVG坐标系中的x位置
  y: number                     // SVG坐标系中的y位置
  status: 'online' | 'offline' | 'warning' | 'error'
  zone: string                  // 所属区域ID
  position: string              // 位置编码
  parameters?: {                // 可选参数
    temperature?: number
    pressure?: number
    vibration?: number
    power?: number
  }
}
            </div>
        </div>

        <div class="demo-section">
            <h2>📊 设备统计</h2>
            <div class="feature-grid">
                <div class="feature-item">
                    <h3>总设备数：21</h3>
                    <p>分布在5个功能区域</p>
                </div>
                <div class="feature-item">
                    <h3>在线设备：14</h3>
                    <p style="color: #4caf50;">正常运行状态</p>
                </div>
                <div class="feature-item">
                    <h3>警告设备：3</h3>
                    <p style="color: #ff9800;">需要关注</p>
                </div>
                <div class="feature-item">
                    <h3>离线设备：4</h3>
                    <p style="color: #f44336;">需要维护</p>
                </div>
            </div>
        </div>

        <div class="demo-section">
            <h2>📚 文档和支持</h2>
            <p>详细的使用文档请参考：<code>src/components/FactoryMap/README.md</code></p>
            <p>组件源码位置：<code>src/components/FactoryMap.vue</code></p>
            <p>演示页面源码：<code>src/views/FactoryMapView.vue</code></p>
        </div>
    </div>
</body>
</html>
